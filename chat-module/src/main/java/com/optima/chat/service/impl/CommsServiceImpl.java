package com.optima.chat.service.impl;

import com.optima.chat.exceptions.ChatOperationException;
import com.optima.chat.exceptions.ChatValidationException;
import com.optima.chat.models.ClosingChatRequest;
import com.optima.chat.service.CommsService;
import com.optima.chat.wsdl.comms.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import org.springframework.ws.soap.client.SoapFaultClientException;
import org.springframework.ws.soap.client.core.SoapActionCallback;


public class CommsServiceImpl extends WebServiceGatewaySupport implements CommsService {

    private static final Logger logger = LogManager.getLogger(CommsServiceImpl.class);

    @Value("${optima.chat.soap.ci.web.comms.ws.url}")
    private String cIWebCommsWs;

    @Override
    public UpdateAliveTimeAndUpdateIsTypingResponse updateAliveTimeAndUpdateIsTyping(UpdateAliveTimeAndUpdateIsTyping updateAliveTimeAndUpdateIsTyping) {
        try {
            UpdateAliveTimeAndUpdateIsTypingResponse response = (UpdateAliveTimeAndUpdateIsTypingResponse) getWebServiceTemplate()
                    .marshalSendAndReceive(this.cIWebCommsWs, updateAliveTimeAndUpdateIsTyping,
                            new SoapActionCallback("http://webservices.ci.ccmm.applications.nortel.com/UpdateAliveTimeAndUpdateIsTyping"));
            if (response != null) {
                return response;
            }
            throw new ChatOperationException("Error while updating alive time. Response is null.");
        } catch (SoapFaultClientException soapFaultClientException) {
            CIDateTime ciDateTime = new CIDateTime();
            ciDateTime.setMilliseconds(-1);
            UpdateAliveTimeAndUpdateIsTypingResponse updateAliveTimeAndUpdateIsTypingResponse = new UpdateAliveTimeAndUpdateIsTypingResponse();
            updateAliveTimeAndUpdateIsTypingResponse.setUpdateAliveTimeAndUpdateIsTypingResult(ciDateTime);
            return updateAliveTimeAndUpdateIsTypingResponse;
        }
    }

    @Override
    public GetContactOnHoldMessagesResponse getContactOnHoldMessages(GetContactOnHoldMessages contactOnHoldMessages) {
        logger.info("Obtaining on hold message.");
        GetContactOnHoldMessagesResponse getContactOnHoldMessagesResponse = (GetContactOnHoldMessagesResponse) getWebServiceTemplate()
                .marshalSendAndReceive(this.cIWebCommsWs, contactOnHoldMessages,
                        new SoapActionCallback("http://webservices.ci.ccmm.applications.nortel.com/GetContactOnHoldMessages"));
        if (getContactOnHoldMessagesResponse != null) {
            logger.info("On hold message has been obtained.");
            return getContactOnHoldMessagesResponse;
        }
        logger.warn("No message has been obtained. Response is null.");
        return new GetContactOnHoldMessagesResponse();
    }

    @Override
    public ReadChatMessageResponse readChatMessage(ReadChatMessage readChatMessage) {
        if (readChatMessage != null) {
            ReadChatMessageResponse readChatMessageResponse = (ReadChatMessageResponse) getWebServiceTemplate()
                    .marshalSendAndReceive(this.cIWebCommsWs, readChatMessage,
                            new SoapActionCallback("http://webservices.ci.ccmm.applications.nortel.com/ReadChatMessage"));
            if (readChatMessageResponse != null) {
                return readChatMessageResponse;
            }
            logger.warn("Obtained chat message is null for request {}", readChatMessage);
            return new ReadChatMessageResponse();
        }
        throw new ChatValidationException("Invalid read chat message request. Request is null.");
    }


    public GetWebOnHoldURLsResponse getWebOnHoldURLsResponse(GetWebOnHoldURLs getWebOnHoldURLs) {
        logger.info("Obtaining web on hold urls.");
        GetWebOnHoldURLsResponse onHoldURLsResponse = (GetWebOnHoldURLsResponse) getWebServiceTemplate()
                .marshalSendAndReceive(this.cIWebCommsWs, getWebOnHoldURLs,
                        new SoapActionCallback("http://webservices.ci.ccmm.applications.nortel.com/GetWebOnHoldURLs"));

        if (onHoldURLsResponse != null) {
            logger.info("Web on hold urls have been obtained.");
            return onHoldURLsResponse;
        }
        logger.warn("Obtained web on hold urls response is null for request {}", getWebOnHoldURLs);
        return new GetWebOnHoldURLsResponse();
    }

    @Override
    public AbandonQueuingWebCommsContactResponse removeChatContactFromWaitingQueue(ClosingChatRequest request) {
        logger.info("Removing chat contact from waiting queue.");
        if (request != null) {
            AbandonQueuingWebCommsContact abandonQueuingWebCommsContact = new AbandonQueuingWebCommsContact();
            abandonQueuingWebCommsContact.setContactID(request.getContactID());
            abandonQueuingWebCommsContact.setSessionKey(request.getSessionKey());
            abandonQueuingWebCommsContact.setClosureComment(request.getClosureComment());
            AbandonQueuingWebCommsContactResponse response = (AbandonQueuingWebCommsContactResponse) getWebServiceTemplate()
                    .marshalSendAndReceive(this.cIWebCommsWs, abandonQueuingWebCommsContact,
                            new SoapActionCallback("http://webservices.ci.ccmm.applications.nortel.com/AbandonQueuingWebCommsContact"));
            logger.info("Removing chat response has been obtained. Result: ", response);
            return response;
        }
        throw new ChatValidationException("Error while trying to remove contact from queue. Required parameters is null.");
    }

    @Override
    public WriteChatMessageResponse sendMessage(WriteChatMessage message) {
        return (WriteChatMessageResponse) getWebServiceTemplate()
                .marshalSendAndReceive(this.cIWebCommsWs, message,
                        new SoapActionCallback("http://webservices.ci.ccmm.applications.nortel.com/WriteChatMessage"));
    }


}
