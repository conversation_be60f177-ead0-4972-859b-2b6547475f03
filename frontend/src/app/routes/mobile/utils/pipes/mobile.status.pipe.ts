import {Pipe, PipeTransform} from '@angular/core';
import {StatusValues} from '../status-values';

@Pipe({
  name: 'statusTranslate'
})
export class SimStatusPipe implements PipeTransform {
  transform(valueStatus: string) {
    if (valueStatus === StatusValues.ACTIVE || valueStatus === StatusValues.INITIACTIVE) {
      return StatusValues.ATTIVA;
    }
    if (valueStatus === StatusValues.PORTEDOUT || valueStatus === StatusValues.DEACTIVATED) {
      return StatusValues.DISATTIVA;
    }
    if (valueStatus === StatusValues.HARDSUSPENSION || valueStatus === StatusValues.LOSTSIM
      || valueStatus === StatusValues.SOFTSUSPENSION) {

      return StatusValues.BLOCCATA;
    }
    return '';
  }
}
