import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import {CommonModule as AngularCommonModule} from '@angular/common';
import {RouterModule} from '@angular/router';
import {CommonModule} from '../../common/common.module';
import {MobileNavbarComponent} from './components/mobile-navbar/mobile-navbar/mobile-navbar.component';
import {AddOptionsLayoutComponent} from './containers/add-options-layout/add-options-layout.component';
import {OfferModificationLayoutComponent} from './containers/offer-modification-layout/offer-modification-layout.component';
import {TopUpSimWithCreditCardLayoutComponent} from './containers/top-up-sim/top-up-sim-with-credit-card-layout/top-up-sim-with-credit-card-layout.component';
import {TrafficDetailsLayoutComponent} from './containers/traffic-details-layout/traffic-details-layout.component';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {SimStatusPipe} from './utils/pipes/mobile.status.pipe';
import {BsDatepickerModule, ModalModule, PopoverModule} from 'ngx-bootstrap';
import {TariffDetailsService} from './service/tarriff-details/tariff-details.service';
import {TariffDetailsResultTableComponent} from './components/tariff-details-result-table/tariff-details-result-table.component';
import {MatMenuModule, MatPaginatorModule, MatTableModule} from '@angular/material';
import {MDBBootstrapModule} from 'angular-bootstrap-md';
import {CdkTableModule} from '@angular/cdk/table';
import {NotificationService} from '../../common/services/notification/notification.service';
import {ConfirmModalComponent} from './components/confirm-modal/confirm-modal.component';
import {TopUpSimSisalLayoutComponent} from './containers/top-up-sim/top-up-sim-sisal-layout/top-up-sim-sisal-layout.component';
import {TopUpSimLayoutComponent} from './containers/top-up-sim/top-up-sim-layout/top-up-sim-layout.component';
import {RichiediPortabilitaComponent} from './containers/richiedi-portabilita/richiedi-portabilita.component';
import {AddOptionsRichiediPortabilitaComponent} from './containers/richiedi-portabilita/add-options-richiedi-portabilita/add-options-richiedi-portabilita.component';
import {OffersService} from '../../common/services/offers/offers.service';
import {TopUpSimWithPaypalComponent} from './containers/top-up-sim/top-up-sim-with-paypal/top-up-sim-with-paypal.component';
import {PayPalService} from '../../common/services/paypal/pay-pal-service.service';
import {PaymentService} from '../../common/services/payment/payment.service';
import {AutoricaricaInformationComponent} from './containers/top-up-sim/autoricarica-information/autoricarica-information.component';

@NgModule({
  imports: [
    CommonModule,
    AngularCommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    BsDatepickerModule.forRoot(),
    MatTableModule,
    MatMenuModule,
    CdkTableModule,
    MatPaginatorModule,
    MDBBootstrapModule.forRoot(),
    ModalModule.forRoot(),
    PopoverModule
  ],
  providers: [TariffDetailsService, NotificationService, OffersService, PayPalService, PaymentService],
  declarations: [
    MobileNavbarComponent,
    AddOptionsLayoutComponent,
    TrafficDetailsLayoutComponent,
    TopUpSimLayoutComponent,
    TopUpSimWithCreditCardLayoutComponent,
    TopUpSimSisalLayoutComponent,
    AddOptionsLayoutComponent,
    OfferModificationLayoutComponent,
    // SafeHtmlPipe,
    SimStatusPipe,
    TariffDetailsResultTableComponent,
    ConfirmModalComponent,
    RichiediPortabilitaComponent,
    AddOptionsRichiediPortabilitaComponent,
    TopUpSimWithPaypalComponent,
    AutoricaricaInformationComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  exports: [
    // SafeHtmlPipe,
    TopUpSimLayoutComponent,
    TopUpSimWithCreditCardLayoutComponent,
    TopUpSimSisalLayoutComponent
  ]
})
export class MobileModule {
}
