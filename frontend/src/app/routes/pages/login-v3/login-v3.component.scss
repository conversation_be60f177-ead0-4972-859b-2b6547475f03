.login-form-input {
  .fa {
    top: 10px;
  }
}
.center-block {
  text-align: center;
}
.asAdmin{
  text-decoration: underline;
  font-size: 10px;
  color: grey;
}
.wrapperBg {
  width: 100%;
  overflow-x: hidden;
  overflow-y: hidden;
  //background: url("/assets/img/newLayout/background.jpg") no-repeat;
  background-size: cover;
  height: 100vh;
  z-index: -1000;
  position: fixed;
}

// Main wrapper
.wrapper {
  width: 100%;
  height: auto;
  min-height: 100%;
  overflow-x: hidden;
  overflow-y: hidden;
}

.btn-primary {
  background-color: #e1523d;
  border-color: #e1523d;
}

.logo {
  height: 50px;
  width: 50%;
  background: url("/assets/img/logo/optima-white.svg") no-repeat top left;
  background-size: contain;
  cursor: pointer;
}
