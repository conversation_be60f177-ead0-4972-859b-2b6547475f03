import {Component, OnInit} from '@angular/core';
import {ScioltoService} from '../../../../common/services/sciolto/sciolto.service';
import {IncidentEventService} from '../../../../common/services/incedentEvent/incident-event.service';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {UserData} from '../../../../common/model/userData.model';
import {OffersService} from '../../../../common/services/offers/offers.service';
import {ServiceResponseStatus} from '../../../../common/enum/ServiceResponseStatus';

@Component({
  selector: 'app-sciolto-gas',
  templateUrl: './sciolto-gas.component.html',
  styleUrls: ['./sciolto-gas.component.scss']
})
export class ScioltoGasComponent implements OnInit {

  @select(['user', 'userInfo'])
  userInfo: Observable<UserData>;
  userCluster: string;
  valoreSpread: number;
  promoTexts: string[] = [];
  withoutIncident: boolean;
  showModalWindowAboutAlreadyOpenIncident: boolean;
  showModalWindowAboutOpenNewIncident: boolean;
  codiceOfferta: string;

  constructor(private scioltoService: ScioltoService, private incidentEventService: IncidentEventService, private offerService: OffersService) {
    this.offerService.checkIncidentEventCrossSelling().subscribe(result => {
      this.withoutIncident = result;
    });
    this.userInfo.subscribe(userInfo => {
      if (userInfo) {
        this.userCluster = userInfo.cluster.value;
      }
    });
    this.scioltoService.getGeneralScioltoInformation().subscribe(generalInformation => {
      if (generalInformation.response[0]) {
        this.codiceOfferta = generalInformation.response[0].fields.Offerta;
        this.scioltoService.getScioltoInformation(generalInformation.response[0].fields.Offerta, this.userCluster).subscribe(information => {
          this.valoreSpread = information.response[0].serviziGas[0].listini[0].costoMateriaPrima;
          const gasPromos = information.response[0].promo.filter(item =>
            item.Elements.some(value => value.NomeServizio === 'GAS')
          );
          const gasPromoOpzionali = information.response[0].promoOpzionali.filter(item =>
            item.Elements.some(value => value.NomeServizio === 'GAS')
          );
          this.promoTexts = [...gasPromos, ...gasPromoOpzionali].map(promo => promo.DescrizioneExt || promo.descrizioneExt);
        });
      }
    });
  }

  ngOnInit() {
  }

  hideModalWindow() {
    this.showModalWindowAboutAlreadyOpenIncident = false;
    this.showModalWindowAboutOpenNewIncident = false;
  }

  buttonClick() {
    if (this.withoutIncident) {
      this.incidentEventService.openIncidentEventForCrossSelling('GAS', this.codiceOfferta).subscribe(response => {
        if (response.status === ServiceResponseStatus.OK) {
          this.showModalWindowAboutOpenNewIncident = true;
          this.withoutIncident = false;
        }
      });
    } else {
      this.showModalWindowAboutAlreadyOpenIncident = true;
    }
  }
}
