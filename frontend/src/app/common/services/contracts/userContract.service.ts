import {Injectable} from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {Observable} from 'rxjs/Observable';

import 'rxjs/add/operator/delay';
import 'rxjs/add/operator/do';

import * as R from 'ramda';
import { Contract } from '../../model/contract/userContract.model';

const trimContract = R.pick(['idContrattoPI', 'dataStipula', 'idCliente', 'id', 'descTipoContratto', 'spRelativeUri', 'canale']) as (_: Contract) => Contract;

@Injectable()
export class ContractService {

  readonly url = 'api/userContracts';
  constructor(private http: HttpClient) {  }

  public getContractData(): Observable<Contract[]> {
    const headers = new HttpHeaders({'clientid':  localStorage.getItem('clientId')});
    return this.http.get<Contract[]>(this.url, {headers: headers})
      .map(contracts => { if (contracts) {return R.map(trimContract, contracts); } else {return null; } });
  }

}
