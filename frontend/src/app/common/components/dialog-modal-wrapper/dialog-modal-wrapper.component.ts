import {Component, EventEmitter, OnDestroy, OnInit, Output, ViewChild} from '@angular/core';
import {DialogModalEntity} from '../../model/dialogModal/DialogModalEntity';
import {select} from '@angular-redux/store';
import {Observable} from 'rxjs/Observable';
import {Subscription} from 'rxjs/Subscription';
import {DialogModalActions} from '../../../redux/dialogModal/actions';
import {DialogModalWindowComponent} from '../dialog-modal-window/dialog-modal-window.component';
import {ObservableUtils} from '../../utils/ObservableUtils';
import {AbstractControl, FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import {RichiediDilazione, RichiediDilazioneDataForm} from '../../../routes/invoices/invoice.model';
import {InvoiceService} from '../../../routes/invoices/invoice.service';
import Validator from '../../utils/Validator';
import {ValidationPatterns} from '../../utils/ValidationPatterns';
import {EditFieldEntity} from '../../model/dialogModal/EditFieldEntity';
import {ModalInputEmitActions} from '../../../redux/dialogModalInputEmit/actions';

@Component({
  selector: 'app-dialog-modal-wrapper',
  templateUrl: './dialog-modal-wrapper.component.html',
  styleUrls: ['./dialog-modal-wrapper.component.scss']
})
export class DialogModalWrapperComponent implements OnInit, OnDestroy {

  @ViewChild(DialogModalWindowComponent)
  childWindow: DialogModalWindowComponent;

  dialogModal: DialogModalEntity;

  @select(['dialogModal', 'dialogModalEntity'])
  dialogModalEntity: Observable<DialogModalEntity>;

  @select(['dialogModal', 'show'])
  shouldShowDialogModal: Observable<boolean>;

  @select(['richiedi'])
  richiediData: Observable<RichiediDilazione>;

  richiedi: RichiediDilazione;

  dialogModalSubscription: Subscription;

  isFontItalic: boolean;

  form: FormGroup;

  scadenzaFormControl: FormControl;

  casualeFormControl: FormControl;

  numeroRateFormControl: FormControl;

  emailFormControl: FormControl;

  phoneNumberFormControl: FormControl;

  importoDilazioneFormControl: FormControl;

  isShowSpinner = false;

  formSubmitted = false;

  hasError = false;

  alterText: string;

  dataForm: RichiediDilazioneDataForm;

  allData = {};

  isCasualeEmpty: boolean = false;

  isScadenxaEmpty: boolean = false;

  isNumeroRateEmpty: boolean = false;

  isImportoDilazioneEmpty: boolean = false;

  isEmailEmpty: boolean = false;

  isPhoneNumberEmpty: boolean = false;

  isModalitaPagamentoEmpty: boolean = false;

  formName = 'richiedi';

  editingfieldNumeroRate: boolean;

  editingfieldEmail: boolean;

  editingfieldPhoneNumber: boolean;

  editingNumeroRate: boolean = false;

  editingEmail: boolean = false;

  editingPhoneNumber: boolean = false;

  marker: string;

  @Output() onFormSubmitted: EventEmitter<any> = new EventEmitter<any>();

  editingFields: Array<EditFieldEntity> = [];

  constructor(private modalActions: DialogModalActions, private formBuilder: FormBuilder, private invoiceService: InvoiceService, private modalInputEmitActions: ModalInputEmitActions) {
  }


  isEditFieldsComplete() {
    if (this.editingFields.every(v => v.isEditing === false) || this.editingFields.length === 0) {
      return true;
    }
  }

  onSubmit() {
    this.isCasualeEmpty = this.casualeFormControl.value.length === 0;
    this.isScadenxaEmpty = this.scadenzaFormControl.value.length === 0;
    this.isImportoDilazioneEmpty = this.importoDilazioneFormControl.value.length === 0;
    this.isNumeroRateEmpty = !this.numeroRateFormControl.value || this.numeroRateFormControl.value.length === 0;
    this.isEmailEmpty = !this.emailFormControl.value || this.emailFormControl.value.length === 0;
    this.isPhoneNumberEmpty = !this.phoneNumberFormControl.value || this.phoneNumberFormControl.value.length === 0;
    this.resetValidationErrors(this.dialogModal.dataFormSetting);
    this.emptyFieldValidator(this.dialogModal.dataFormSetting);
    this.dataForm = this.form.value;
    this.setImportoDilazioneValues(this.dataForm);
    this.allData['formModalData'] = this.dataForm;
    this.editingNumeroRate = this.editingfieldNumeroRate;
    this.editingEmail = this.editingfieldEmail;
    this.editingPhoneNumber = this.editingfieldPhoneNumber;
    if (this.isValidForm(this.dataForm) && this.isEditFieldsComplete()) {
      const secondModal = {
        title: `Richiedi Dilazione`,
        customTitle: true,
        customTitleStyle: {
          'margin': '0',
          'width': '40%',
          'font-weight': '100',
          'font-size': '22px'
        },
        hasDataForm: true,
        dataFormSetting: [
          {
            labelText: 'Importo dilazione',
            labelType: 'text',
            value: parseFloat(this.dataForm.importoDilazione).toFixed(2),
            rowStyle: {'background': '#f0f5f9'},
            formName: 'importo'
          },
          {
            labelText: 'Numero rate',
            labelType: 'text',
            value: this.dataForm.numeroRate,
            formName: 'numero',
            rowStyle: {'background': '#ffffff'}
          },
          {
            labelText: 'Importo rata',
            labelType: 'text',
            value: (parseFloat(this.dataForm.importoDilazione) / parseFloat(this.dataForm.numeroRate)).toFixed(2),
            formName: 'importoRata',
            rowStyle: {'background': '#f0f5f9'}
          },
          {
            labelText: 'Cadenza rate',
            labelType: 'text',
            value: this.dataForm.scadenzaRate,
            rowStyle: {'background': '#ffffff'},
            formName: 'cadenza'
          },
          {
            labelText: 'Data prima rata',
            labelType: 'text',
            value: this.richiedi.maxDataPrimaRata,
            formName: 'primarata',
            rowStyle: {'background': '#f0f5f9'}
          },
        ],
        hasButtons: true,
        nextEntities: [],
        withAnullaButton: true,
        callBackOnSubmitFirstModal: this.dialogModal.callBackOnSubmitFirstModal
      } as DialogModalEntity;

      this.displayNewModal(secondModal);
    }
  }

  onSubmitSecondModal() {
    this.isShowSpinner = true;
    let finalModal = {};
    this.dataForm = this.form.value;
    this.allData['resultFormModalData'] = this.form.value;
    this.invoiceService.saveRichiediDilazione(this.allData).subscribe(resp => {
        if (resp['response'].ramo) {
          finalModal = {
            text: this.getFinalModalText(resp['response'].ramo),
            img: '/assets/img/icons/ok.png',
          } as DialogModalEntity
        }
        if (resp['response'].errorMsg) {
          finalModal = {
            text: '',
            img: '/assets/img/icons/error.png',
          } as DialogModalEntity
        }
      },
      error1 => {

      },
      () => {
        this.isShowSpinner = false;
        this.dialogModal.callBackOnSubmitFirstModal();
        this.displayNewModal(finalModal);
      });
  }

  getFinalModalText(ramo: string): string {
    if (ramo === 'B' || ramo === 'C') {
      return 'La tua richiesta di dilazione è stata presa in carico, ' +
        'a breve sarai contattato dal servizio clienti ' +
        'Optima per un riscontro in merito';
    }
    if (ramo === 'A') {
      return `La richiesta di dilazione è stata
        accettata, a breve inoltreremo il piano di
        rientro con il dettaglio delle rate al tuo
        indirizzo e-mail ` + this.allData['formModalData'].email;
    }
  }

  isPlaceHolder(formName) {
    if (formName === 'casuale') {
      if (this.casualeFormControl.value === '') {
        return {color: 'gray'};
      } else return {color: '#36749C'}
    }
    if (formName === 'importoDilazione') {
      if (this.importoDilazioneFormControl.value === '') {
        return {color: 'gray'};
      } else return {color: '#36749C'}
    }
    if (formName === 'scadenzaRate') {
      if (this.scadenzaFormControl.value === '') {
        return {color: 'gray'};
      } else return {color: '#36749C'}
    }
  }

  ngOnInit() {
    this.dialogModalSubscription = this.dialogModalEntity.subscribe(entity => {
      this.dialogModal = entity;

      if (this.dialogModal) {
        this.isFontItalic = this.dialogModal.isFontItalic;
        this.buildForm();
      }
    });
  }

  buildForm() {
    let settings = {};
    if (this.dialogModal.dataFormSetting) {
      this.richiediData.subscribe(res => {
        this.richiedi = res['richiedi'];
      });
      this.dialogModal.dataFormSetting.forEach(item => {
        switch (item.needValidation) {
          case ('maxNumber') : {
            settings[item.formName] = [item.value, [Validator.isGreaterThan(this.richiedi.maxNumRate), Validator.digits()]];
            break;
          }
          case ('text') : {
            if (item.formName === 'email') {
              settings[item.formName] = [item.value, [Validator.pattern(ValidationPatterns.email, 'Il valore inserito non è valido.')]];
            } else if (item.formName === 'phoneNumber') {
              settings[item.formName] = [item.value, [Validator.pattern(ValidationPatterns.phoneNumber, 'Il valore inserito non è valido.'),
                Validator.digits(),
                Validators.minLength(8),
                Validators.maxLength(15)]
              ];
            } else {
              settings[item.formName] = [item, [Validators.required]];
            }
            break
          }
          case ('dropdown') : {
            settings[item.formName] = [item.dropdownValues.reverse()[0].value];
            break;
          }
          default :
            settings[item.formName] = [item.value];
        }
      });
      this.form = this.formBuilder.group(settings);
      this.scadenzaFormControl = <FormControl>this.form.controls['scadenzaRate'];
      this.casualeFormControl = <FormControl>this.form.controls['casuale'];
      this.numeroRateFormControl = <FormControl>this.form.controls['numeroRate'];
      this.emailFormControl = <FormControl>this.form.controls['email'];
      this.phoneNumberFormControl = <FormControl>this.form.controls['phoneNumber'];
      this.importoDilazioneFormControl = <FormControl>this.form.controls['importoDilazione']
    } else {
      this.form = this.formBuilder.group({
        inputField: [
          null,
          [
            Validators.required,
            Validators.minLength(4),
            Validators.maxLength(4)
          ]
          // this.asyncInputFieldValidator.bind(this)
        ]
      });
      this.form.get('inputField').valueChanges.subscribe(value => {
        this.hasError = false;
      });
    }
  }

  checkForEditingFields(isEditing: any) {
    switch (isEditing.name) {
      case 'numeroRate' : {
        if (isEditing.isEditing) {
          this.editingfieldNumeroRate = true;
          this.pushToEditingFieldsArray(isEditing);
        } else {
          if (isEditing.action) {
            this.pushToEditingFieldsArray(isEditing);
            this.editingNumeroRate = false;
          }
          this.editingfieldNumeroRate = false;
        }
        break;
      }
      case 'email' : {
        if (isEditing.isEditing) {
          this.pushToEditingFieldsArray(isEditing);
          this.editingfieldEmail = true;
        } else {
          if (isEditing.action) {
            this.pushToEditingFieldsArray(isEditing);
            this.editingEmail = false;
          }
          this.editingfieldEmail = false;
        }
        break;
      }
      case 'phoneNumber' : {
        if (isEditing.isEditing) {
          this.pushToEditingFieldsArray(isEditing);
          this.editingfieldPhoneNumber = true;
        } else {
          if (isEditing.action) {
            this.pushToEditingFieldsArray(isEditing);
            this.editingPhoneNumber = false;
          }
          this.editingfieldPhoneNumber = false;
        }
        break;
      }
    }
  }

  pushToEditingFieldsArray(item) {
    if (this.editingFields.length !== 0) {
      this.editingFields.forEach(i => {
        if (i.name === item.name) {
          i.isEditing = item.isEditing;
        } else this.editingFields.push(item);
      });
    } else {
      this.editingFields.push(item);
    }
  }

  setImportoDilazioneValues(data) {
    if (data.importoDilazione === 'Richiesta Dilazione Importo scaduto') {
      if (this.dataForm.canoneRai == 'true') {
        data.importoDilazione = this.richiedi.importoDilazionabileSoloScaduto;
      } else {
        data.importoDilazione = this.richiedi.importoDilazionabileSoloScadutoSenzaRai;
      }
      data.flagScaduto = true;
    }
    if (data.importoDilazione === 'Richiesta Dilazione Importo Scaduto e in scadenza (Tot. Esposizione)') {
      if (this.dataForm.canoneRai == 'true') {
        data.importoDilazione = this.richiedi.importoDilazionabileTotale;
      } else {
        data.importoDilazione = this.richiedi.importoDilazionabileTotaleSenzaRai;
      }
      data.flagScaduto = false;
    }


    if (data.importoDilazione === 'Importo scaduto') {
      if (this.dataForm.canoneRai == 'true') {
        data.importoDilazione = this.richiedi.importoDilazionabileSoloScaduto;
      } else {
        data.importoDilazione = this.richiedi.importoDilazionabileSoloScadutoSenzaRai;
      }
      data.flagScaduto = true;
    }
    if (data.importoDilazione === 'Importo in scadenza') {
      if (this.dataForm.canoneRai == 'true') {
        data.importoDilazione = this.richiedi.importoDilazionabileTotale;
      } else {
        data.importoDilazione = this.richiedi.importoDilazionabileTotaleSenzaRai;
      }
      data.flagScaduto = false;
    }


  }

  onChangeSelected(formName, value) {
    if (formName === 'importoDilazione') {
      if (value === '1: Richiesta Dilazione Importo scaduto') {
        this.numeroRateFormControl.setValidators([Validator.isGreaterThan(this.richiedi.maxNumRateSoloScaduto)]);
      } else {
        this.numeroRateFormControl.setValidators([Validator.isGreaterThan(this.richiedi.maxNumRate)]);
      }
    }
  }

  getConfirmationText(formName: string): string {
    switch (formName) {
      case 'numeroRate': {
        return 'Il numero di rate inserite per la richiesta di dilazione è '
          + this.form.controls['numeroRate'].value +
          ' per completare l’operazione clicca su “Conferma”';
      }
      case 'email': {
        return 'L\'indirizzo mail inserito è '
          + this.form.controls['email'].value +
          ' per completare l’operazione clicca su “Conferma”';
      }
      case 'phoneNumber': {
        return 'Il numero di telefono inserito è '
          + this.form.controls['phoneNumber'].value +
          ' per completare l’operazione clicca su “Conferma”';
      }
    }
  }


  asyncInputFieldValidator(input: AbstractControl) {
    return this.dialogModal.callbackToExecuteOnSubmit(input.value).map(res => {
      return res.clientId ? null : {'inputError': true};
    });
  }

  executeFunctionBySubmit() {
    this.dialogModal.callbackToExecuteOnSubmit().toPromise().then(resp => {
      if (resp.status && resp.status === 'KO') {
        this.alterText = resp.message;
      }
      this.refreshModalEntity(new DialogModalEntity());
    });
  }

  emptyFieldValidator(dataSetting: Array<any>): void {
    for (const item of dataSetting) {
      if (this.form.controls[item.formName].value === null ||
        this.form.controls[item.formName].value === '') {
        item.hasError = true;
        this.dialogModal.dataFormSetting[item.formName] = item.hasError;
      }
    }
  }

  resetValidationErrors(dataSetting: Array<any>): void {
    for (let item of dataSetting) {
      if (item.needValidation) {
        item.hasError = false;
      }
    }
  }

  isDisabledCanoneRai(): boolean {
    return this.richiedi.importoDilazionabileSoloScadutoSenzaRai < 50 || this.richiedi.importoDilazionabileTotaleSenzaRai < 50;
  }

  isValidForm(data: RichiediDilazioneDataForm): boolean {
    return !(data.scadenzaRate === null
      || data.casuale ===''
      || data.scadenzaRate === ''
      || data.numeroRate == null
      || data.email === null
      || data.email === ''
      || data.phoneNumber === null
      || data.importoDilazione === ''
      || data.phoneNumber === '');

  }

  executeNext(event?) {
    this.dialogModal.hasFillField ? this.processInputData(event) : this.processNextModal();
  }

  processInputData(event) {
    event.preventDefault();
    this.formSubmitted = true;
    if (this.form.valid) {

      this.dialogModal.callbackToExecuteOnSubmit(this.form.get('inputField').value).map(res => {
        return res;
      }).subscribe(res => {
        if (res.clientId) {
          if (this.dialogModal.callbackByValidationResult) {
            this.dialogModal.callbackByValidationResult();
          }
          this.modalInputEmitActions.emitInputFieldValue(true);
          this.refreshModalEntity(new DialogModalEntity());
        } else {
          this.hasError = true;
        }
      }, error => {
        this.hasError = true;
      });
    } else {
      this.hasError = true;
    }
  }

  processNextModal() {
    if (this.dialogModal.callbackToExecuteOnSubmit) {
      this.dialogModal.callbackToExecuteOnSubmit()
        .toPromise().then(resp => {
        if (resp.status && resp.status === 'KO') {
          this.alterText = resp.message;
        }
        this.refreshModalEntity(new DialogModalEntity());
      });
    } else {
      this.refreshModalEntity(new DialogModalEntity());
    }
  }

  refreshModalEntity(nextDialogModalEntity: DialogModalEntity) {
    if (this.dialogModal.nextEntities) {
      nextDialogModalEntity = this.dialogModal.nextEntities[0];
      if (nextDialogModalEntity['marker'] === 'amazon') {
        this.marker = nextDialogModalEntity['marker'];
      }
      if (this.alterText) {
        Object.keys(nextDialogModalEntity).forEach(function (index) {
          nextDialogModalEntity[index] = null;
        });
        nextDialogModalEntity.text = this.alterText;
      }
      this.alterText = null;
      nextDialogModalEntity.nextEntities = this.dialogModal.nextEntities.slice(1);
      this.displayNewModal(nextDialogModalEntity);
    } else {
      this.modalActions.hideDialogModal();
    }
  }

  displayNewModal(nextDialogModalEntity: DialogModalEntity) {
    this.modalActions.showDialogModal(nextDialogModalEntity);
  }

  ngOnDestroy(): void {
    ObservableUtils.unsubscribeAll([this.dialogModalSubscription]);
  }
}
