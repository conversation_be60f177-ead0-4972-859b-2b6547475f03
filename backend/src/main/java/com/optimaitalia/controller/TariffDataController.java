package com.optimaitalia.controller;

import com.optimaitalia.model.db.TariffData;
import com.optimaitalia.service.TariffDataService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "api/tariff")
public class TariffDataController {

    private final TariffDataService tariffDataService;

    public TariffDataController(TariffDataService tariffDataService) {
        this.tariffDataService = tariffDataService;
    }

    @GetMapping(value = "get/tariff")
    public TariffData getTariffByCountry(@RequestParam(name = "country") String country) {
        return tariffDataService.getTariffByCountry(country);
    }

    @GetMapping(value = "get/tariff/roaming")
    public TariffData getRoamingTariffByCountry(@RequestParam(name = "country") String country) {
        return tariffDataService.getTariffRoamingByCountry(country);
    }

}
