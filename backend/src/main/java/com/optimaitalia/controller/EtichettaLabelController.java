package com.optimaitalia.controller;

import com.optimaitalia.model.db.EtichettaLabel;
import com.optimaitalia.service.EtichettaLabelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/label")
public class EtichettaLabelController {

    private final EtichettaLabelService labelService;

    @Autowired
    public EtichettaLabelController(EtichettaLabelService labelService) {
        this.labelService = labelService;
    }

    @GetMapping("/{clientId}/{pod}")
    @PreAuthorize("#clientId!=null&&@checkPreAuthorizeUtil.checkForUserRole(#clientId, authentication.principal.uid)")
    public ResponseEntity<EtichettaLabel> getEtichettaLabelByPodUtNumber(@PathVariable String clientId, @PathVariable String pod) {
        EtichettaLabel etichettaLabel = labelService.getEtichettaLabelByPodUtNumber(pod);
        return new ResponseEntity<>(etichettaLabel, HttpStatus.OK);
    }

    @PostMapping
    @PreAuthorize("#etichettaLabel.clientId!=null&&" +
            "@checkPreAuthorizeUtil.checkForUserRole(#etichettaLabel.clientId, authentication.principal.uid)")
    public ResponseEntity<EtichettaLabel> createOrUpdateEtichettaLabel(@RequestBody EtichettaLabel etichettaLabel) {
        EtichettaLabel newEtichettaLabel = labelService.createOrUpdateEtichettaLabel(etichettaLabel);
        return new ResponseEntity<>(newEtichettaLabel, HttpStatus.CREATED);
    }
}
