package com.optimaitalia.repository;

import com.optimaitalia.model.db.TariffData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface TariffDataRepository extends JpaRepository<TariffData, String> {

    @Query(value = "select tdata from TariffData tdata JOIN ItaliaVsEstero italia on tdata.zone = italia.zone where italia.countryName = ?1")
    TariffData getTariffByCountry(String country);

    @Query(value = "select tdata from TariffData tdata JOIN RoamingToItalia italia on tdata.zone = italia.zone where italia.countryName = ?1")
    TariffData getTariffRoamingByCountry(String country);

}
