package com.optimaitalia.configuration;

import com.optimaitalia.builders.UserDataChangeBuilder;
import com.optimaitalia.builders.UserDataChangeBuilderImpl;
import com.optimaitalia.model.enums.IncidentEventCategory;
import com.optimaitalia.model.enums.ShippingType;
import com.optimaitalia.model.wrappers.user.requests.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Configuration
public class UserDataConfiguration {
    @Bean
    protected UserDataChangeBuilder userDataChangeBuilder() {
        UserDataChangeBuilder<PersonalDataChangeRequest, Change> userDataChangeBuilder = new UserDataChangeBuilderImpl<>();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        userDataChangeBuilder.addBuilder(IncidentEventCategory.CHANGE_EMAIL, (request) -> {
            String format = dateTimeFormatter.format(LocalDateTime.now());
            PersonalDataChange personalDataChange = new PersonalDataChange();
            personalDataChange.setChangeType(224);
            personalDataChange.setApplyDate(format);
            personalDataChange.setEmail(request.getValue());
            return personalDataChange;
        });
        userDataChangeBuilder.addBuilder(IncidentEventCategory.CHANGE_SOCIAL_NAME, (request) -> {
            String format = dateTimeFormatter.format(LocalDateTime.now());
            PersonalDataChange personalDataChange = new PersonalDataChange();
            personalDataChange.setChangeType(224);
            personalDataChange.setApplyDate(format);
            personalDataChange.setCompanyName(request.getValue());
            return personalDataChange;
        });
        userDataChangeBuilder.addBuilder(IncidentEventCategory.CHANGE_TELEPHONE_NUMBER, (request) -> {
            String format = dateTimeFormatter.format(LocalDateTime.now());
            PersonalDataChange personalDataChange = new PersonalDataChange();
            personalDataChange.setChangeType(224);
            personalDataChange.setApplyDate(format);
            personalDataChange.setPhoneNumber(request.getValue());
            return personalDataChange;
        });
        userDataChangeBuilder.addBuilder(IncidentEventCategory.CHANGE_INVOICE_SHIPPING_METHOD, (request) -> {
            String format = dateTimeFormatter.format(LocalDateTime.now());
            ChangeShippingType changeShippingType = new ChangeShippingType();
            changeShippingType.setChangeType(201);
            changeShippingType.setApplyDate(format);
            ShippingType currentShippingType = null;
            ShippingType newShippingType = null;

            if (ShippingType.ELETTRONICA.name().equals(request.getValue())) {
                newShippingType = ShippingType.ELETTRONICA;
                currentShippingType = ShippingType.CARTACEA;
            }
            if (ShippingType.CARTACEA.name().equals(request.getValue())) {
                currentShippingType = ShippingType.ELETTRONICA;
                newShippingType = ShippingType.CARTACEA;
            }
            if (newShippingType == null) {
                throw new RuntimeException("Not allowed.");
            }
            changeShippingType.setCurrentShippingTypeId(currentShippingType.gedId());
            changeShippingType.setCurrentShippingType(currentShippingType);
            changeShippingType.setNewShippingTypeId(newShippingType.gedId());
            changeShippingType.setNewShippingType(newShippingType);
            return changeShippingType;
        });
        userDataChangeBuilder.addBuilder(IncidentEventCategory.PEC_VARIATION, (request) -> {
            PecChange change = new PecChange();
            String format = dateTimeFormatter.format(LocalDateTime.now());
            change.setApplyDate(format);
            change.setStartingDate(format);
            change.setPec(request.getValue());
            return change;
        });

        userDataChangeBuilder.addBuilder(IncidentEventCategory.CHANGE_RECIPIENT_CODE, (request) -> {
            RecipientChange change = new RecipientChange();
            change.setNewCustomerDestCode(request.getValue());
            return change;
        });
        return userDataChangeBuilder;
    }
}
