package com.optimaitalia.service;

import com.optima.security.model.changeCustomerEmail.ChangeEmailRequest;
import com.optima.security.model.changeCustomerEmail.ChangeEmailResponse;
import com.optima.security.model.newUserService.RequestNewUserService;
import com.optima.security.model.newUserService.ResponseNewUserService;
import com.optimaitalia.model.optimaResponseRequest.IsUserResponse;

public interface OptimaService {
    IsUserResponse isUserExist(String userName);
    ResponseNewUserService addNewUser(RequestNewUserService user);
    ChangeEmailResponse changeEmail(ChangeEmailRequest changeEmailRequest);
}
