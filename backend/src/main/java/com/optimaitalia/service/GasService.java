package com.optimaitalia.service;

import com.optima.common.exceptions.ValidateException;
import com.optimaitalia.model.wrappers.gas.*;

import java.util.List;

public interface GasService {

    List<PodDetail> findPodDetails(List<PodDetailsRequest> podDetails);

    List<PodDetail> findPodDetails(PodDetailsRequest podDetails) throws ValidateException;

    List<PdrAdditionalData> getPdrAdditionalData(PdrAdditionalDataRequest pdrAdditionalDataRequest) throws ValidateException;

    List<PdrAdditionalData> getPdrAdditionalData(List<PdrAdditionalDataRequest> additionalDataRequests);

    List<GasPointAdjustment> gasPointAdjustments(String clientId, String pdr);
}
