package com.optimaitalia.model.wrappers.voice;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.optimaitalia.model.wrappers.voip.CredenzialiRadius;
import com.optimaitalia.model.wrappers.voip.CredenzialiSIP;
import com.optimaitalia.utils.dateDeserializer.ContractsDateDeserializer;

import java.util.Date;
import java.util.List;

public class VoiceClientCli {

    private Integer idCli;

    private String dialer;

    private String vlanId;

    private String stato;

    private String statoOperazione;

    private String tipoOperazione;

    @JsonDeserialize(using = ContractsDateDeserializer.class)
    private Date dataInvioCarrier;

    private String tipoContratto;

    private String tipoNumero;

    private String tipoAccesso;

    private Boolean dialerPrincipale;

    private String tipoCarrier;

    private Integer idFatturazione;

    private List<VariazioniLinea> variazioniLinea;

    private List<Opzioni> opzioni;

    private CredenzialiSIP credenzialiSIP;
     private CredenzialiRadius credenzialiRadius;
//
//    private List<Object> blocchi;
//
//    private List<Object> alert;

    private Object adslRiferimento;

    private String idAdsl;

    private String dataDac;

    public String getVlanId() {
        return vlanId;
    }

    public void setVlanId(String vlanId) {
        this.vlanId = vlanId;
    }

    public Integer getIdCli() {
        return idCli;
    }

    public void setIdCli(Integer idCli) {
        this.idCli = idCli;
    }

    public String getDialer() {
        return dialer;
    }

    public void setDialer(String dialer) {
        this.dialer = dialer;
    }

    public String getStato() {
        return stato;
    }

    public void setStato(String stato) {
        this.stato = stato;
    }

    public String getStatoOperazione() {
        return statoOperazione;
    }

    public void setStatoOperazione(String statoOperazione) {
        this.statoOperazione = statoOperazione;
    }

    public String getTipoOperazione() {
        return tipoOperazione;
    }

    public void setTipoOperazione(String tipoOperazione) {
        this.tipoOperazione = tipoOperazione;
    }

    public Date getDataInvioCarrier() {
        return dataInvioCarrier;
    }

    public void setDataInvioCarrier(Date dataInvioCarrier) {
        this.dataInvioCarrier = dataInvioCarrier;
    }

    public String getTipoContratto() {
        return tipoContratto;
    }

    public void setTipoContratto(String tipoContratto) {
        this.tipoContratto = tipoContratto;
    }

    public String getTipoNumero() {
        return tipoNumero;
    }

    public void setTipoNumero(String tipoNumero) {
        this.tipoNumero = tipoNumero;
    }

    public String getTipoAccesso() {
        return tipoAccesso;
    }

    public void setTipoAccesso(String tipoAccesso) {
        this.tipoAccesso = tipoAccesso;
    }

    public Boolean getDialerPrincipale() {
        return dialerPrincipale;
    }

    public void setDialerPrincipale(Boolean dialerPrincipale) {
        this.dialerPrincipale = dialerPrincipale;
    }

    public String getTipoCarrier() {
        return tipoCarrier;
    }

    public void setTipoCarrier(String tipoCarrier) {
        this.tipoCarrier = tipoCarrier;
    }

    public Integer getIdFatturazione() {
        return idFatturazione;
    }

    public void setIdFatturazione(Integer idFatturazione) {
        this.idFatturazione = idFatturazione;
    }

    public List<VariazioniLinea> getVariazioniLinea() {
        return variazioniLinea;
    }

    public void setVariazioniLinea(List<VariazioniLinea> variazioniLinea) {
        this.variazioniLinea = variazioniLinea;
    }

    public Object getAdslRiferimento() {
        return adslRiferimento;
    }

    public void setAdslRiferimento(Object adslRiferimento) {
        this.adslRiferimento = adslRiferimento;
    }

    public String getIdAdsl() {
        return idAdsl;
    }

    public void setIdAdsl(String idAdsl) {
        this.idAdsl = idAdsl;
    }

    public String getDataDac() {
        return dataDac;
    }

    public void setDataDac(String dataDac) {
        this.dataDac = dataDac;
    }

    public List<Opzioni> getOpzioni() {
        return opzioni;
    }

    public void setOpzioni(List<Opzioni> opzioni) {
        this.opzioni = opzioni;
    }

    public CredenzialiSIP getCredenzialiSIP() {
        return credenzialiSIP;
    }

    public void setCredenzialiSIP(CredenzialiSIP credenzialiSIP) {
        this.credenzialiSIP = credenzialiSIP;
    }

    public CredenzialiRadius getCredenzialiRadius() {
        return credenzialiRadius;
    }

    public void setCredenzialiRadius(CredenzialiRadius credenzialiRadius) {
        this.credenzialiRadius = credenzialiRadius;
    }
}
