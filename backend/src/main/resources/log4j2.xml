<?xml version="1.0" encoding="UTF-8"?>
<Configuration monitorInterval="60">
    <Properties>
        <Property name="log-path">${sys:log.path:-/tmp/logs/areaclienti}</Property>
        <Property name="archive">${log-path}/archive</Property>
        <Property name="log-path-5-anni">${sys:log.path.longtime:-/tmp/logs/areaclienti}</Property>
        <Property name="archive-5-anni">${log-path-5-anni}/archive</Property>
        <Property name="hostname">${env:HOSTNAME}</Property>
    </Properties>

    <Appenders>

        <Console name="consoleAppender" target="SYSTEM_OUT">
            <PatternLayout>
                <pattern>
                    [%-5level] %d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %c{1} - %msg%n
                </pattern>>
            </PatternLayout>
        </Console>

        <RollingFile name="errorFileAppender" fileName="${log-path}/self-care-errors.log"
                     filePattern="${archive}/$${date:dd-MM-yyyy}/self-care-errors%d{dd-MM-yyyy-HH-mm-ss}-%i.log.gz">
            <PatternLayout>
                <pattern>
                    [%-5level] %d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %c{1} - %msg%n
                </pattern>
            </PatternLayout>
            <Policies>
                <SizeBasedTriggeringPolicy size="10 MB"/>
            </Policies>
        </RollingFile>

        <RollingFile name="infoFileAppender" fileName="${log-path}/self-care-logging.log"
                     filePattern="${archive}/$${date:dd-MM-yyyy}/self-care-%d{dd-MM-yyyy-HH-mm-ss}-%i.log.gz">
            <PatternLayout>
                <pattern>
                    [%-5level] %d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %c{1} - %msg%n
                </pattern>
            </PatternLayout>
            <Policies>
                <SizeBasedTriggeringPolicy size="10 MB"/>
            </Policies>
        </RollingFile>

        <RollingFile name="frontendLogInfo" fileName="${log-path-5-anni}/${hostname}_self-care-frontend-logging.log"
                     filePattern="${archive-5-anni}/$${date:dd-MM-yyyy}/self-care-%d{dd-MM-yyyy-HH-mm-ss}-%i.log.gz">
            <PatternLayout>
                <pattern>
                    [%-5level] %d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %c{1} - %msg%n
                </pattern>
            </PatternLayout>
            <Policies>
                <SizeBasedTriggeringPolicy size="10 MB"/>
            </Policies>
        </RollingFile>

        <!--<RollingFile name="chatInfoFileAppender" fileName="${log-path}/self-care-chat.log"-->
                     <!--filePattern="${archive}/$${date:dd-MM-yyyy}/self-care-chat-%d{dd-MM-yyyy-HH-mm-ss}-%i.log.gz">-->
            <!--<PatternLayout>-->
                <!--<pattern>-->
                    <!--[%-5level] %d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %c{1} - %msg%n-->
                <!--</pattern>-->
            <!--</PatternLayout>-->
            <!--<Policies>-->
                <!--<SizeBasedTriggeringPolicy size="10 MB"/>-->
            <!--</Policies>-->
        <!--</RollingFile>-->

        <!--<RollingFile name="chatErrorsFileAppender" fileName="${log-path}/chat-errors.log"-->
                     <!--filePattern="${archive}/$${date:dd-MM-yyyy}/chat-errors-%d{dd-MM-yyyy-HH-mm-ss}-%i.log.gz">-->
            <!--<PatternLayout>-->
                <!--<pattern>-->
                    <!--[%-5level] %d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %c{1} - %msg%n-->
                <!--</pattern>-->
            <!--</PatternLayout>-->
            <!--<Policies>-->
                <!--<SizeBasedTriggeringPolicy size="10 MB"/>-->
            <!--</Policies>-->
        <!--</RollingFile>-->
    </Appenders>

    <Loggers>
        <!--<Logger name="com.optima.chat">-->
            <!--<AppenderRef ref="chatInfoFileAppender" level="info"/>-->
            <!--<AppenderRef ref="chatErrorsFileAppender" level="error"/>-->
        <!--</Logger>-->
        <!--<Logger name="com.optimaitalia">-->
            <!--<AppenderRef ref="errorFileAppender" level="error"/>-->
            <!--<AppenderRef ref="infoFileAppender" level="info"/>-->
        <!--</Logger>-->

        <Logger name="com.optimaitalia.controller.LoggerController" level="info">
            <AppenderRef ref="frontendLogInfo" level="info"/>
        </Logger>

        <Root level="info">
            <AppenderRef ref="consoleAppender" level="info"/>
            <AppenderRef ref="errorFileAppender" level="error"/>
            <AppenderRef ref="infoFileAppender" level="info"/>
        </Root>
    </Loggers>
</Configuration>
